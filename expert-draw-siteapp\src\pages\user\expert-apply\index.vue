<!-- 专家抽取申请页面 -->
<template>
  <div class="expert-apply-page">
    <!-- 头部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">成都市农业农村局项目专家抽取</div>
      </div>
    </div>
    <!-- 需求部门显示 -->
    <div class="department-section">
      <van-cell-group>
        <van-cell
          title="需求部门"
          :value="formData.department"
          label="机关处室/直属单位"
        />
      </van-cell-group>
    </div>
    <!-- 表单区域 -->
    <div class="form-container">
      <van-form ref="formRef" required="auto">
        <!-- 项目类型 - 单选框 -->
        <van-field
          name="type"
          label="项目类型"
          :rules="[{ required: true, message: '请选择项目类型' }]"
        >
          <template #input>
            <van-radio-group v-model="formData.type" direction="horizontal">
              <van-radio name="GOVERNMENT">机关</van-radio>
              <van-radio name="DIRECTLY">直属单位</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <!-- 需求部门 -->
        <van-field
          v-model="formData.name"
          name="department"
          label="专项资金（项目）名称"
          placeholder="请输入"
        >
        </van-field>

        <!-- 专项资金名称 -->
        <van-field
          v-model="formData.funds"
          name="name"
          label="专项资金金额"
          suffix="万元"
          type="number"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入专项资金金额' }]"
        >
          <template #button> 万元 </template>
        </van-field>
        <!-- 评审开始时间 -->
        <DateTimePicker
          v-model="formData.reviewDate"
          :field-props="{
            name: 'reviewDate',
            label: '评审开始时间',
            placeholder: '请选择评审开始时间',
            rules: [{ required: true, message: '请选择评审开始时间' }],
          }"
          title="选择评审开始时间"
        />

        <!-- 预计天数 -->
        <van-field
          v-model="formData.dateNum"
          name="dateNum"
          label="预计天数"
          placeholder="请输入"
          type="digit"
          :rules="[{ required: true, message: '请输入预计天数' }]"
        >
          <template #button> 天 </template>
        </van-field>

        <!-- 评审地点 -->
        <van-field
          v-model="formData.reviewPlace"
          name="reviewPlace"
          label="评审地点"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入评审地点' }]"
        />

        <!-- 项目评审工作方案备案表 -->
        <FileUploader
          v-model="formData.attachments"
          :field-props="{
            name: 'attachments',
            label: '项目评审工作方案备案表',
          }"
          accept="*"
          required
          :rules="[{ required: true, message: '请上传项目评审工作方案备案表' }]"
        />

        <!-- 项目初审意见反馈表 -->
        <FileUploader
          v-model="formData.attachmentsFirst"
          :field-props="{
            name: 'attachmentsFirst',
            label: '项目初审意见反馈表',
          }"
          accept="*"
        />

        <!-- 其他 -->
        <FileUploader
          v-model="formData.attachmentsOther"
          :field-props="{ name: 'attachmentsOther', label: '其他' }"
          accept="*"
        />
      </van-form>
    </div>

    <!-- 评审专家人员建议 -->
    <div class="expert-suggestion">
      <div class="section-title">评审专家人员建议</div>

      <!-- 抽取专家类型 -->
      <van-cell-group>
        <van-cell
          title="抽取专家"
          :value="getExpertSourceText(formData.proposalExpertSources)"
          is-link
          @click="showExpertSourcePicker = true"
        />
      </van-cell-group>

      <!-- 专家类型和人数 -->
      <div class="expert-types">
        <div class="section-header">
          专家行业类别及人数
          <span class="required-mark">*</span>
        </div>

        <!-- 有数据时显示列表 -->
        <div v-if="formData.planDtos.length > 0" class="expert-list">
          <div
            class="expert-type-item"
            v-for="(item, index) in formData.planDtos"
            :key="index"
          >
            <div class="type-row">
              <span class="type-label">{{
                getExpertTypeName(item.expertTypeId)
              }}</span>
              <div class="type-actions">
                <van-stepper
                  v-model="item.expertNum"
                  min="1"
                  max="20"
                  @change="updateTotalCount"
                  class="expert-stepper"
                />
                <van-button
                  size="mini"
                  type="danger"
                  icon="delete-o"
                  @click="removeExpertType(index)"
                  class="delete-btn"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态显示 -->
        <div v-else class="empty-state">
          <div class="empty-content">
            <van-icon name="search" size="24" color="#c8c9cc" />
            <div class="empty-text">暂无专家类型数据</div>
            <div class="empty-hint">请点击下方按钮添加专家类型</div>
          </div>
        </div>
      </div>

      <!-- 添加专家类型 -->
      <van-button
        type="success"
        size="small"
        icon="plus"
        @click="addExpertType"
        class="add-expert-btn"
      >
        添加专家类型
      </van-button>
      <div
        style="padding-left: 16px; font-size: 14px; color: #323233"
        v-show="formData.planDtos.length"
      >
        抽取专家人数：{{ formData.planExpertPlNum }}
      </div>
      <!-- 统计信息 -->
      <div class="expert-stats">
        <van-field
          name="departmentPlNum"
          placeholder="请输入"
          required
          v-model="formData.departmentPlNum"
          type="digit"
          label="经办处室或直属单位人数"
          :rules="[{ required: true, message: '请输入人数' }]"
        ></van-field>
        <van-field
          name="inviteExpertPlNum"
          placeholder="请输入"
          required
          v-model="formData.inviteExpertPlNum"
          type="digit"
          label="邀请专家人数"
          :rules="[{ required: true, message: '请输入人数' }]"
        ></van-field>
        <van-field
          v-model="formData.approvalLeader"
          label="审批领导"
          required
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入审批领导' }]"
        />
        <van-field
          v-model="formData.description"
          label="补充说明"
          placeholder="请输入"
          type="textarea"
          rows="3"
        />
      </div>
    </div>
    <!-- 申请人信息 -->
    <div class="applicant-info">
      <van-cell-group>
        <van-cell title="需求申请人姓名" :value="formData.applyUserName" />
        <van-cell title="需求申请人联系方式" :value="formData.applyUserPhone" />
      </van-cell-group>
    </div>
    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button type="default" size="normal" @click="onCancel"
        >返回</van-button
      >
      <van-button type="default" size="normal" @click="onSave">暂存</van-button>
      <van-button type="success" size="normal" @click="onSubmit"
        >提交</van-button
      >
    </div>

    <!-- 专家来源选择器 -->
    <van-popup v-model:show="showExpertSourcePicker" position="bottom">
      <van-picker
        :columns="expertSourceOptions"
        @confirm="onExpertSourceConfirm"
        @cancel="showExpertSourcePicker = false"
        title="选择专家来源"
      />
    </van-popup>

    <!-- 专家类型选择器 -->
    <van-popup v-model:show="showExpertTypePicker" position="bottom">
      <van-picker
        :columns="expertTypes"
        @confirm="onExpertTypeConfirm"
        @cancel="showExpertTypePicker = false"
        title="选择专家类型"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onActivated } from "vue";
import { useRouter,useRoute } from "vue-router";
import {
  showToast,
  showSuccessToast,
  showConfirmDialog,
} from "vant";
import { expertApi } from "@/api/expert";
import { useUserStore } from "@/stores";
import moment from "moment";
import DateTimePicker from "@/components/DateTimePicker.vue";
import useExpert from "@/hook/expert.js";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const loading = ref(false);
const showExpertTypePicker = ref(false);
const showExpertSourcePicker = ref(false);

// 获取当前日期时间的字符串格式
const getCurrentDateTimeString = () => {
  return moment().format("YYYY-MM-DD HH:mm:ss");
};
// 表单数据
const formData = reactive({
  type: "GOVERNMENT",
  department: userStore.departmentName,
  name: "",
  funds: "", // 默认为当前日期时间
  reviewDate: getCurrentDateTimeString(), // 默认为当前日期时间
  dateNum: "",
  reviewPlace: "",
  attachments: [],
  attachmentsFirst: [],
  attachmentsOther: [],
  proposalExpertSources: "OUR",
  planDtos: [],
  departmentPlNum: "",
  inviteExpertPlNum: "",
  approvalLeader: "",
  description: "",
  applyUserName: "",
  applyUserPhone: "",
  districtDep: false,
  planExpertPlNum: "",
  inviteDtos: [],
});

// 专家类型数据
const { expertTypes } = useExpert();

// 专家来源选项
const expertSourceOptions = window.$enums.getEnums("ProposalExpertSourcesType")

// 计算属性
const totalExpertCount = computed(() => {
  return formData.planDtos.reduce((total, item) => total + item.expertNum, 0);
});

//获取名称
const getExpertTypeName = (typeId) => {
  return expertTypes.value.find((x) => x.id == typeId)?.name;
};

const getExpertSourceText = (sourceValue) => {
  const source = expertSourceOptions.find(
    (option) => option.value === sourceValue
  );
  return source ? source.text : "请选择";
};

const updateTotalCount = () => {
  formData.planExpertPlNum = totalExpertCount.value;
};

const addExpertType = () => {
  showExpertTypePicker.value = true;
};

const removeExpertType = async (index) => {
  try {
    await showConfirmDialog({
      title: "确认删除",
      message: "确定要删除这个专家类型吗？",
    });

    formData.planDtos.splice(index, 1);
    updateTotalCount();
    showSuccessToast("删除成功");
  } catch (error) {
    // 用户取消删除
  }
};

const onExpertTypeConfirm = ({ selectedOptions }) => {
  const selectedType = selectedOptions[0];
  // 直接添加新的专家类型（允许重复）
  formData.planDtos.push({
    expertTypeId: selectedType.value,
    expertNum: 1,
  });

  updateTotalCount();
  showExpertTypePicker.value = false;
};

const onExpertSourceConfirm = ({ selectedOptions }) => {
  formData.proposalExpertSources = selectedOptions[0].value;
  showExpertSourcePicker.value = false;
};

const onSubmit = async () => {
  try {
    await formRef.value.validate();
    // 验证专家类型不能为空
    if (formData.planDtos.length === 0) {
      showToast("请至少添加一个专家类型");
      return;
    }

    loading.value = true;

    // 准备提交数据
    const submitData = {
      ...formData,
      attachments: formData.attachments.map((file) => file.id || file),
      attachmentsFirst: formData.attachmentsFirst.map((file) => file.id || file),
      attachmentsOther: formData.attachmentsOther.map((file) => file.id || file),
    };

    await expertApi.submitExpertApply(submitData);
    // 跳转到成功页面
    router.replace("/pub/result");
  } finally {
    loading.value = false;
  }
};

const onSave = async () => {
  try {
    loading.value = true;
    formData.attachments = formData.attachments.map((file) => file.id || file),
    formData.attachmentsFirst = formData.attachmentsFirst.map((file) => file.id || file),
    formData.attachmentsOther = formData.attachmentsOther.map((file) => file.id || file),
    await expertApi.submitExpertApply({...formData,submit:false});
    showSuccessToast("暂存成功");
  } finally {
    loading.value = false;
  }
};

const onCancel = () => {
  router.back();
};

// 获取详情数据
const loadDetail = async (id) => {
  try {
    loading.value = true;
    const response = await expertApi.getApplyDetail(id);
    for (let key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        formData[key] = response.data[key]
      }
    }
    formData.id = id; 
    updateTotalCount();
  } finally {
    loading.value = false;
  }
};

// 初始化用户信息到表单
const initUserInfo = () => {
  if (userStore.userInfo) {
    formData.applyUserName = userStore.userName;
    formData.applyUserPhone = userStore.userInfo.phone;
  }
};

// 生命周期
onMounted(async () => {
  if (route.query.id) {
    loadDetail(route.query.id);
  } else {
    updateTotalCount();
    initUserInfo();
  }
});

onActivated(() => {
  updateTotalCount();
  initUserInfo();
});
</script>

<style lang="scss" scoped>
.expert-apply-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding-bottom: 80px;
  font-size: 16px;
}

.header-section {
  background: var(--van-primary-color);
  line-height: 42px;
  color: white;
  text-align: center;
}

.title-line {
  font-weight: 500;
}

.department-section {
  ::v-deep() {
    .van-cell__value {
      display: flex;
      align-items: center;
      color: #000;
      justify-content: end;
    }
  }
}

.form-container {
  background: white;
  margin: 16px 0;
  overflow: hidden;
}

.expert-suggestion {
  margin: 16px 0;
  background: white;
  overflow: hidden;
}

.section-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  background: var(--van-background);
  border-bottom: 1px solid var(--van-border-color);
}

.expert-types {
  padding: 16px;
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  color: var(--van-text-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--van-border-color);
  position: relative;
}

.required-mark {
  color: var(--van-danger-color);
  margin-left: 4px;
}

.expert-list {
  margin-top: 16px;
}

.expert-type-item {
  margin-bottom: 12px;
}

.empty-state {
  padding: 16px 0;
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.empty-text {
  color: var(--van-text-color-2);
  font-size: 14px;
  margin: 0;
}

.empty-hint {
  color: var(--van-text-color-3);
  font-size: 12px;
  margin: 0;
}

.type-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--van-border-color);

  &:last-child {
    border-bottom: none;
  }
}

.type-label {
  font-size: 14px;
  color: var(--van-text-color);
  flex: 1;
}

.type-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.expert-stepper {
  :deep(.van-stepper__input) {
    width: 50px;
    text-align: center;
  }
}

.number-stepper {
  :deep(.van-stepper__input) {
    width: 60px;
    text-align: center;
  }
}

.delete-btn {
  min-width: 32px;
  height: 32px;
}

.add-expert-btn {
  margin: 16px;
  width: calc(100% - 32px);
}

.expert-stats,
.applicant-info {
  background: white;
  overflow: hidden;
  margin: 16px 0;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--van-border-color);
  display: flex;
  gap: 12px;
  z-index: 100;

  .van-button {
    flex: 1;
  }
}

// 单选框样式优化
:deep(.van-radio-group) {
  display: flex;
  gap: 16px;

  &[data-direction="vertical"] {
    flex-direction: column;
    gap: 8px;
  }

  &[data-direction="horizontal"] {
    flex-direction: row;
  }
}

:deep(.van-radio) {
  margin-right: 0;
}

:deep(.van-field__label) {
  width: 120px;
  flex: none;
}
</style>

<route lang="json5">
{
  name: "ExpertApply",
  meta: {
    title: "专家抽取申请",
  },
}
</route>
