<!-- 测试预算项目金额抽取代理机构申请表 -->
<template>
  <div class="agency-apply-page">
    <!-- 头部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">
          【{{ projectData.name }}】抽取代理机构申请表
        </div>
      </div>
    </div>

    <!-- 基本信息 -->
    <van-cell-group>
      <van-cell title="申请部门" :value="formData.department" />
      <van-cell title="经办人" :value="formData.applyUserName" />
      <van-cell title="经办人联系方式" :value="formData.applyUserPhone" />
    </van-cell-group>

    <!-- 一、项目基本情况 -->
    <div class="section">
      <div class="title">一、项目基本情况</div>
      <van-cell-group>
        <van-cell title="项目名称" :value="projectData.projectName" />
        <van-cell title="经费预算" :value="projectData.fundsBudget + '万元'" />
        <van-cell
          title="采购预算(仅政府采购项目)"
          :value="projectData.procurementBudget"
        />
        <van-cell
          title="无预算项目"
          :value="projectData.priceUnit ? '是' : '否'"
        />
      </van-cell-group>
    </div>

    <!-- 二、申请用途 -->
    <div class="section">
      <div class="title">二、申请用途</div>
      <div class="purpose-list">
        <div class="purpose-item">
          <div>需求论证</div>
          <van-checkbox
            v-model="formData.purposeDemonstration"
            shape="square"
          ></van-checkbox>
        </div>
        <div class="purpose-item">
          <div>实施采购</div>
          <van-checkbox
            v-model="formData.purposePurchase"
            shape="square"
          ></van-checkbox>
        </div>
        <div class="purpose-item">
          <div>履约验收</div>
          <van-checkbox
            v-model="formData.purposeAcceptance"
            shape="square"
          ></van-checkbox>
        </div>
      </div>
    </div>

    <!-- 三、提供附件 -->
    <div class="section">
      <div class="title">三、提供附件</div>
      <div class="attachment-desc">
        领导批示、党组会议纪要、党组会请示议题材料、政府采购计划备案表等
      </div>
      <FileUploader
        v-model="formData.attachments"
        :field-props="{
          name: 'attachmentsFirst',
          label: '',
        }"
        accept="*"
      />
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <van-button type="primary" class="submit-btn" @click="handleSubmit">
        提交申请
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showConfirmDialog } from "vant";
import { expertApi } from "@/api/expert";
import { apply } from "@/api/procurement";
import { useUserStore } from "@/stores";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
//项目数据
const projectData = ref({});

// 表单数据
const formData = ref({
  applyUserName: userStore.userName,
  applyUserPhone: userStore.userInfo.phone,
  department: userStore.departmentName,
  purposeDemonstration: false,
  purposePurchase: false,
  purposeAcceptance: false,
  attachments: [],
  projectId: route.params.id,
});

// 获取项目详情数据
const loadProjectDetail = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await expertApi.projectDetail(id);
    projectData.value = response.data;
  } finally {
    loading.value = false;
  }
};

// 提交申请
const handleSubmit = async () => {
  await showConfirmDialog({
    title: "确认提交？",
    message: "备案申请一旦提交，无法修改，请仔细审查后再提交！",
  });
  formData.value.attachments = formData.value.attachments.map((file) => file.id || file);
  // 调用提交接口
  await apply(formData.value);

  showToast("提交成功");
  router.back();
};

loadProjectDetail();
</script>

<style lang="scss" scoped>
.agency-apply-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
  padding-bottom: 80px;

  .header-section {
    background: linear-gradient(135deg, #07c160 0%, #05a854 100%);
    padding: 20px 16px;
    color: white;

    .header-content {
      text-align: center;

      .title-line {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.4;
      }
    }
  }

  .section {
    background-color: #fff;
    margin-top: 12px;

    .title {
      padding: 16px;
      font-weight: bold;
      font-size: 16px;
      color: #323233;
      border-bottom: 1px solid #f7f8fa;
    }
  }

  .purpose-list {
    padding: 16px;

    .purpose-item {
      padding: 8px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom:1px solid var(--van-gray-2);
    }
  }

  .attachment-desc {
    padding: 0 16px 16px;
    font-size: 12px;
    color: #969799;
    line-height: 1.5;
  }

  .upload-section {
    padding: 0 16px 16px;

    :deep(.van-uploader) {
      .van-uploader__upload {
        width: 80px;
        height: 80px;
        background: #f7f8fa;
        border: 1px dashed #c8c9cc;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #646566;

        .van-icon {
          font-size: 24px;
          margin-bottom: 4px;
        }
      }

      .van-uploader__preview {
        width: 80px;
        height: 80px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }

  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: white;
    border-top: 1px solid #ebedf0;

    .submit-btn {
      width: 100%;
      height: 44px;
      background: #07c160;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>

<route lang="json5">
{
  name: "AgencyApply",
  meta: {
    title: "抽取代理机构申请",
    requiresAuth: true,
    keepAlive: false,
  },
}
</route>
