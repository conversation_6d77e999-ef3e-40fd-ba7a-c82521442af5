<!-- 用户端首页 -->
<template>
  <div class="home-page">
    <!-- 头部绿色渐变区域 -->
    <div class="header-section"></div>

    <!-- 功能按钮区域 -->
    <div class="function-buttons">
      <div class="button-row">
        <div class="function-button" @click="handleExpertApply">
          <div class="button-icon expert-icon">
            <img src="@/assets/user/home/<USER>" alt="专家申请" />
          </div>
          <div class="button-text">抽取专家申请</div>
        </div>
        <div class="function-button" @click="handleAgencyApply">
          <div class="button-icon agency-icon">
            <img src="@/assets/user/home/<USER>" alt="代理机构申请" />
          </div>
          <div class="button-text">抽取代理机构申请</div>
        </div>
      </div>
    </div>

    <!-- 列表功能区域 -->
    <div class="list-functions">
      <van-cell-group>
        <van-cell
          title="项目申报记录查询"
          is-link
          @click="handleProjectRecords"
          icon="search"
        />
        <van-cell
          title="查看专家资料库"
          is-link
          @click="handleExpertDatabase"
          icon="contact"
        />
      </van-cell-group>
    </div>

    <!-- 底部信息 -->
    <div class="footer-info">
      <div style="color: var(--van-blue);margin-bottom: 20px;">
        <router-link to="/gov/home">访问管理端</router-link>
      </div>
      <div class="contact-info">
        <div>电话：028-61887395</div>
        <div>地址：成都市高新区天府软件园</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";

const router = useRouter();

const handleExpertApply = () => {
  router.push("/user/expert-apply");
};

const handleAgencyApply = () => {
  router.push("/user/agency-list");
};

const handleProjectRecords = () => {
  router.push("/user/project-records");
};

const handleExpertDatabase = () => {
  router.push("/pub/expert");
};

</script>

<style lang="scss" scoped>
.home-page {
  height: calc(100vh - 46px);
}

.header-section {
  height: 164px;
  background: url(@/assets/user/home/<USER>
  background-size: 100% 100%;
}

.function-buttons {
  padding: 0 12px;
  .button-row {
    display: flex;
    gap: 12px;
    height: 154px;
    .function-button {
      flex: 1;
      padding: 12px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &:active {
        transform: scale(0.98);
      }
      .button-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 72px;
          height: 72px;
          border-radius: 24px;
        }
      }

      .button-text {
        font-size: 16px;
        font-weight: 500;
        color: var(--van-text-color);
        margin-top: 12px;
      }
    }
  }
}

.list-functions {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.footer-info {
  margin-top: 40px;
  padding: 20px;
  text-align: center;
}

.contact-info {
  font-size: 12px;
  color: var(--van-text-color-2);
  line-height: 1.5;
}
</style>

<route lang="json5">
{
  name: "Home",
  meta: {
    title: "专家&代理机构抽取系统",
  },
}
</route>
