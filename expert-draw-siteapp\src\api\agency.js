/**
 * 代理机构相关API接口
 */
export const agencyApi = {
  /**
   * 获取代理机构列表
   * @param {Object} params - 查询参数
   * @param {number} [params.pageSize] - 每页条数，默认20
   * @param {string} [params.keyword] - 搜索关键词（机构名称、联系人、电话）
   * @returns {Promise} 返回代理机构列表数据
   */
  getAgencyList: (params = {}) => {
    return window.$http.fetch('/api/v1/agency', {
      pageSize: 20,
      ...params
    })
  },

  /**
   * 获取代理机构详情
   * @param {string} id - 代理机构ID
   * @returns {Promise} 返回代理机构详情
   */
  getAgencyDetail: (id) => {
    return window.$http.fetch(`/api/v1/agency/${id}`)
  },

  /**
   * 创建代理机构
   * @param {Object} data - 代理机构数据
   * @param {string} data.name - 机构名称
   * @param {string} data.contact - 经办人1
   * @param {string} data.phone - 经办人1电话
   * @param {string} data.contactb - 经办人2
   * @param {string} data.phoneb - 经办人2电话
   * @param {string} data.leader - 负责人
   * @param {string} data.leaderPhone - 负责人电话
   * @param {string} data.serviceRange - 服务范围
   * @returns {Promise} 返回创建结果
   */
  createAgency: (data) => {
    return window.$http.post('/api/v1/agency', data)
  },

  /**
   * 更新代理机构
   * @param {string} id - 代理机构ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateAgency: (id, data) => {
    return window.$http.put(`/api/v1/agency/${id}`, data)
  },

  /**
   * 删除代理机构
   * @param {string} id - 代理机构ID
   * @returns {Promise} 返回删除结果
   */
  deleteAgency: (id) => {
    return window.$http.delete(`/api/v1/agency/${id}`)
  },

  /**
   * 启用/禁用代理机构
   * @param {string} id - 代理机构ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise} 返回操作结果
   */
  toggleAgencyStatus: (id, enabled) => {
    return window.$http.put(`/api/v1/agency/${id}/status`, { enabled })
  },

  /**
   * 提交代理机构申请
   * @param {Object} data - 申请数据
   * @param {string} data.projectId - 项目ID
   * @param {string} data.department - 申请部门
   * @param {string} data.operator - 经办人
   * @param {string} data.operatorPhone - 经办人联系方式
   * @param {string} data.projectName - 项目名称
   * @param {number} data.fundsBudget - 经费预算
   * @param {number} data.procurementBudget - 采购预算
   * @param {string} data.noBudgetProject - 无预算项目
   * @param {Object} data.purposes - 申请用途
   * @param {Array} data.attachments - 附件列表
   * @returns {Promise} 返回申请结果
   */
  submitAgencyApply: (data) => {
    return window.$http.post('/api/v1/agency-apply', data)
  },

  /**
   * 获取代理机构申请详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回申请详情
   */
  getAgencyApplyDetail: (id) => {
    return window.$http.fetch(`/api/v1/agency-apply/${id}`)
  },

  /**
   * 上传申请附件
   * @param {File} file - 文件对象
   * @returns {Promise} 返回上传结果
   */
  uploadApplyAttachment: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'agency-apply')

    return window.$http.post('/api/v1/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
