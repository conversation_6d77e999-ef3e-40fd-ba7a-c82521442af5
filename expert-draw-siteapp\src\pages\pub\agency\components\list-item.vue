<template>
  <!-- 专家头像和基本信息 -->
  <div class="header">
    <div class="basic">
      <div class="name">{{ item.agency.name }}</div>
      <div class="status"></div>
    </div>
  </div>

  <!-- 专家详细信息 -->
  <div class="details">
    <div class="detail-row">
      <div class="detail-item">
        <span class="label">代理机构联系人</span>
        <span class="value">{{ item.agency.contact }}</span>
      </div>
      <div class="detail-item">
        <span class="label">联系电话</span>
        <span class="value">{{ item.agency.phone }}</span>
      </div>
    </div>

    <div class="detail-row">
      <div class="detail-item">
        <span class="label">操作人</span>
        <span class="value">{{ item.createUser.nickname }}</span>
      </div>
      <div class="detail-item">
        <span class="label">操作时间</span>
        <span class="value">{{ item.modifyTime }}</span>
      </div>
    </div>

    <div class="detail-row">
      <div class="detail-item">
        <span class="label">备注说明</span>
        <span class="value">{{ item.selectIn }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: [],
});


</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .basic {
    flex: 1;

    .name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        &:empty::after {
          content: "--";
        }
      }
    }
  }
}
</style>
