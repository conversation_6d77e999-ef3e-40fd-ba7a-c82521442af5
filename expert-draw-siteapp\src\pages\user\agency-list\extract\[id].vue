<!-- 代理机构抽取页面 -->
<template>
  <div class="records-page">
    <FuniList :tabs="tabsConfig">
      <template #item="{ item, index }"> 
      <agencyItem :item="item"></agencyItem>  
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { detail } from "@/api/procurement";
import agencyItem from "@/pages/pub/agency/components/list-item.vue";

const router = useRouter();
const route = useRoute();

async function loadData(params) {
  const id = route.params.id;
  const response = await detail(id);
  let data = response.data.drawResults
  return {
    data: data,
    total: data.length,
  };
}

// Tabs 配置
const tabsConfig = [
  {
    key: "one",
    title: "代理机构",
    loadFunction: loadData,
    showSearch: false,
  },
];
</script>

<style lang="scss" scoped>
.records-page {
  height: calc(100vh - 46px);
}
</style>

<route lang="json5">
{
  name: "agencyExtract",
  meta: {
    title: "抽取代理机构",
  },
}
</route>
