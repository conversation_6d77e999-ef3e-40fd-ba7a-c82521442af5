<!-- 审核步骤组件 -->
<template>
  <div class="auth-step">
    <!-- 进度内容 -->
    <div class="progress-container">
      <!-- 进度步骤 -->
      <div class="steps-section">
        <div class="custom-steps">
          <div
            class="step-item"
            v-for="(step, index) in flows"
            :key="index"
            :class="{ active: step.activity }"
          >
            <div class="step-icon" :class="{ active: step.activity }">
              {{ index + 1 }}
            </div>
            <div class="step-title">{{ step.conf.flowNode }}</div>
            <div
              v-if="index < flows.length - 1"
              class="step-line"
              :class="{ active: step.activity }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 动态生成的审批节点 -->
      <template v-for="(flow, index) in flows">
        <div :key="index" class="section" v-if="flow.enabled">
          <div class="section-title">{{ getFlowNodeTitle(flow) }}</div>

          <!-- 审批人信息 -->
          <van-cell-group>
            <van-cell
              :title="getApproverTitle(flow)"
              :value="getApproverName(flow)"
            />
            <van-cell
              v-if="index == 0"
              title="经办人联系方式"
              :value="getApproverContact(flow)"
            />
            <van-cell
              v-if="index > 0"
              title="任职岗位"
              :value="getApproverPost(flow)"
            />
            <van-cell
              v-if="index > 0"
              title="任职处室"
              :value="getApproverDept(flow)"
            />
            <van-cell
              v-if="index == 0"
              title="需求申请时间"
              :value="formatDateTime(flow.flowTime)"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="审批意见"
              :value="flow.approvalStatus == 'PASS' ? '通过' : '驳回'"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="审批时间"
              :value="flow.approvalTime"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="补充说明"
              :value="flow.approvalContent"
            />
          </van-cell-group>
        </div>
      </template>

      <!-- 审批意见 -->
      <van-form
        ref="approvalFormRef"
        required="auto"
        :scroll-to-error="true"
        scroll-to-error-position="center"
        @submit="onSubmit"
      >
        <van-field
          name="approvalStatus"
          label="审批意见"
          :rules="[{ required: true, message: '请选择审批意见' }]"
        >
          <template #input>
            <van-radio-group
              v-model="approvalForm.approvalStatus"
              direction="horizontal"
            >
              <van-radio name="PASS">通过</van-radio>
              <van-radio name="REJECT">驳回</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="approvalForm.approvalContent"
          name="approvalContent"
          label="补充说明"
          type="textarea"
          placeholder="请输入补充说明"
          rows="4"
          maxlength="200"
          show-word-limit
          :rules="[{ required: true, message: '请输入补充说明' }]"
          class="comment-field"
        />
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import moment from "moment";
import { useUserStore } from "@/stores";

const props = defineProps({
  flows: [],
});

const { userInfo } = useUserStore();
const approvalFormRef = ref(null);

// 审批表单数据
const approvalForm = ref({
  approvalStatus: "",
  approvalContent: "",
});

// 格式化时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "-";
  return moment(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

// 获取流程节点标题
const getFlowNodeTitle = (flow) => {
  return flow.conf?.flowNode;
};

// 获取审批人标题
const getApproverTitle = (flow) => {
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return "经办人姓名";
  } else if (flowNode.includes("部门") || flowNode.includes("负责人")) {
    return "审批人";
  } else if (flowNode.includes("审计") || flowNode.includes("监督")) {
    return "审批人";
  }
  return "审批人";
};

// 获取审批人姓名
const getApproverName = (flow) => {
  if (flow.activity) {
    return userInfo.nickname;
  } else {
    return flow.approvalUser?.nickname || "-";
  }
};

// 获取审批人联系方式
const getApproverContact = (flow) => {
  if (flow.activity) {
    return userInfo.phone;
  } else {
    return flow.approvalUser?.phone;
  }
};

// 获取审批人岗位
const getApproverPost = (flow) => {
  if (flow.activity) {
    return userInfo.post || "--";
  } else {
    return flow.approvalUser?.post || "--";
  }
};

// 获取审批人部门
const getApproverDept = (flow) => {
  if (flow.activity) {
    return userInfo.groups?.[0]?.name || "--";
  } else {
    return flow.approvalUser?.groups?.[0]?.name || "--";
  }
};

let validateCallBack;
/**
 * 验证
 */
function validate() {
  return new Promise((resolve, reject) => {
    validateCallBack = resolve;
    approvalFormRef.value.submit();
  });
}

function onSubmit() {
  const flow = props.flows.find((x) => x.activity);
  validateCallBack({ id: flow.id, ...approvalForm.value });
}

defineExpose({
  validate,
});
</script>

<style lang="scss" scoped>
.auth-step {
  position: relative;
  .progress-container {
    padding: 16px;
  }

  .steps-section {
    background: white;
    padding: 20px 16px;
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .custom-steps {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;

    &:not(:last-child) {
      margin-right: 16px;
    }
  }

  .step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ebedf0;
    color: #969799;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    z-index: 2;
    position: relative;

    &.active {
      background: #07c160;
      color: white;
    }
  }

  .step-title {
    font-size: 12px;
    color: #646566;
    text-align: center;
    line-height: 1.2;
  }

  .step-line {
    position: absolute;
    top: 12px;
    left: calc(50% + 12px);
    right: calc(-50% + 12px);
    height: 1px;
    background: #ebedf0;
    z-index: 1;

    &.active {
      background: #07c160;
    }
  }

  .step-item:last-child .step-line {
    display: none;
  }
  .section {
    background: white;
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    padding: 16px;
  }
}
</style>
