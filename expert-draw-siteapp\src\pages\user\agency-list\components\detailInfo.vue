<template>
  <div class="detail-info" v-if="projectData">
    <!-- 基本信息 -->
    <van-cell-group>
      <van-cell title="申请部门" :value="projectData.department" />
      <van-cell title="经办人" :value="projectData.applyUserName" />
      <van-cell title="经办人联系方式" :value="projectData.applyUserPhone" />
    </van-cell-group>

    <!-- 一、项目基本情况 -->
    <div class="section">
      <div class="title">一、项目基本情况</div>
      <van-cell-group>
        <van-cell title="项目名称" :value="projectData.project.projectName" />
        <van-cell
          title="经费预算"
          :value="projectData.project.fundsBudget + '万元'"
        />
        <van-cell
          title="采购预算(仅政府采购项目)"
          :value="projectData.project.procurementBudget"
        />
        <van-cell
          title="无预算项目"
          :value="projectData.project.priceUnit ? '是' : '否'"
        />
      </van-cell-group>
    </div>

    <!-- 二、申请用途 -->
    <div class="section">
      <div class="title">二、申请用途</div>
      <van-cell-group>
        <van-cell
          title="需求论证"
          :value="projectData.purposeDemonstration ? '是' : '否'"
        />
        <van-cell
          title="实施采购"
          :value="projectData.purposePurchase ? '是' : '否'"
        />
        <van-cell
          title="履约验收"
          :value="projectData.purposeAcceptance ? '是' : '否'"
        />
      </van-cell-group>
    </div>

    <!-- 三、提供附件 -->
    <div class="section">
      <div class="title">三、提供附件</div>
      <div class="attachment-desc">
        领导批示、党组会议纪要、党组会请示议题材料、政府采购计划备案表等
      </div>
      <FileUploader
        v-model="projectData.attachments"
        disabled
      />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  projectData: {
    type: Object,
  },
});
</script>

<style lang="scss" scoped>
.detail-info {
  .section {
    background-color: #fff;
    margin-top: 12px;

    .title {
      padding: 16px;
      font-weight: bold;
      font-size: 16px;
      color: #323233;
      border-bottom: 1px solid #f7f8fa;
    }
  }

  .purpose-list {
    padding: 16px;

    .purpose-item {
      padding: 8px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--van-gray-2);
    }
  }

  .attachment-desc {
    padding: 0 16px 16px;
    font-size: 12px;
    color: #969799;
    line-height: 1.5;
  }

  .upload-section {
    padding: 0 16px 16px;

    :deep(.van-uploader) {
      .van-uploader__upload {
        width: 80px;
        height: 80px;
        background: #f7f8fa;
        border: 1px dashed #c8c9cc;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #646566;

        .van-icon {
          font-size: 24px;
          margin-bottom: 4px;
        }
      }

      .van-uploader__preview {
        width: 80px;
        height: 80px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
