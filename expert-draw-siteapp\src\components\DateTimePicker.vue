<!-- 日期时间选择器组件 -->
<template>
  <div class="datetime-picker">
    <!-- 表单字段 -->
    <van-field
      v-model="displayValue"
      placeholder="请选择"
      v-bind="fieldProps"
      readonly
      is-link
      @click="showPicker"
    />
    <van-popup
      v-model:show="data.isPicker"
      position="bottom"
      round
      @close="confirmOn"
      :close-on-click-overlay="false"
    >
      <van-picker
        ref="picker"
        title="请选择时间"
        :columns="data.columns"
        @change="onChange"
        @cancel="cancelOn"
        @confirm="onConfirm"
        v-model="data.selectedValues"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from "vue";
import moment from 'moment'

const data = reactive({
  isPicker: false, //是否显示弹出层
  columns: [], //所有时间列
  selectedValues: [], //控件选择的时间值
});
const props = defineProps({
  // 绑定值
  modelValue: {
    type: Array,
    default: () => []
  },
  // 传入的显影状态
  showPicker: {
    type: Boolean,
    default: false,
  },
  // 传入的值
  values: {
    type: String,
    default: "",
  },
  fieldProps: {
    type: Object,
    default: () => ({}),
  },
  // 最小日期
  minDate: {
    type: String,
    default: "",
  },
  // 最大日期
  maxDate: {
    type: String,
    default: "",
  },
});

const displayValue = ref("");
//定义要向父组件传递的事件
const emit = defineEmits(["update:modelValue", "confirm"]);

watch(()=>props.modelValue,()=>{
  displayValue.value = props.modelValue;
})
watch(
  () => props.showPicker,
  (val) => {
    data.isPicker = val;
    data.columns = [];
    getcolumns();
  },
  {
    immediate: true, //立即监听--进入就会执行一次 监听显影状态
  }
);
function onChange(e) {
  data.selectedValues = e.selectedValues;
  if (e.columnIndex === 0 || e.columnIndex === 1) {
    updateMonthAndDay(data.selectedValues[0], data.selectedValues[1]);
  }
}
function getcolumns() {
  let dateVaules;
  if (props.modelValue) {
    let strtime = props.modelValue; //传入的时间
    let date = new Date(strtime.replace(/-/g, "/"));
    let timeVaules = date.getTime();
    dateVaules = new Date(timeVaules);
  } else {
    dateVaules = new Date(); //没有传入时间则默认当前时刻
  }
  let Y = dateVaules.getFullYear();
  let M = dateVaules.getMonth();
  let D = dateVaules.getDate();
  let h = dateVaules.getHours();
  let m = dateVaules.getMinutes();
  let s = dateVaules.getSeconds();

  // 计算年份范围
  let minYear = new Date().getFullYear() - 10;
  let maxYear = new Date().getFullYear() + 10;

  debugger
  if (props.minDate) {
    const minDateObj = new Date(props.minDate.replace(/-/g, "/"));
    minYear = Math.max(minYear, minDateObj.getFullYear());
  }

  if (props.maxDate) {
    const maxDateObj = new Date(props.maxDate.replace(/-/g, "/"));
    maxYear = Math.min(maxYear, maxDateObj.getFullYear());
  }

  let year = []; //获取年份数组
  year.values = [];
  for (let i = minYear; i <= maxYear; i++) {
    year.push({ text: i.toString() + "年", value: i.toString() });
  }
  year.defaultIndex = year.values.indexOf(Y); //设置默认选项当前年

  // 个位数补0
  const _M = M < 9 ? `0${M + 1}` : (M + 1).toString(); //月份比实际获取的少1，所以要加1
  const _D = D < 10 ? `0${D}` : D.toString();
  const _h = h < 10 ? `0${h}` : h.toString();
  const _m = m < 10 ? `0${m}` : m.toString();
  const _s = s < 10 ? `0${s}` : s.toString();
  // 生成年月日时分秒时间值
  data.selectedValues.push(Y.toString());
  data.selectedValues.push(_M);
  data.selectedValues.push(_D);
  data.selectedValues.push(_h);
  data.selectedValues.push(_m);
  data.selectedValues.push(_s);
  data.columns.push(year); //生成年列

  // 生成月份数组，考虑日期范围限制
  let month = generateMonthOptions(Y);
  data.columns.push(month); //生成月列

  // 生成日期数组，考虑日期范围限制
  let day = generateDayOptions(Y, M + 1);
  data.columns.push(day); //生成日列

  let hour = []; //创建小时数组
  hour = Object.keys(Array.apply(null, { length: 24 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "时", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "时", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "时",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(hour); //生成小时列

  let mi = []; //创建分钟数组
  mi = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "分", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "分", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "分",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(mi); //生成分钟列

  let ss = []; //创建秒数数组
  ss = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "秒", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "秒", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "秒",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(ss); //生成秒钟列
}

function getCountDays(year, month) {
  //获取某年某月多少天
  let day = new Date(year, month, 0);
  return day.getDate();
}

// 生成月份选项，考虑日期范围限制
function generateMonthOptions(year) {
  let minMonth = 1;
  let maxMonth = 12;

  if (props.minDate) {
    const minDateObj = new Date(props.minDate.replace(/-/g, "/"));
    if (year === minDateObj.getFullYear()) {
      minMonth = minDateObj.getMonth() + 1;
    }
  }

  if (props.maxDate) {
    const maxDateObj = new Date(props.maxDate.replace(/-/g, "/"));
    if (year === maxDateObj.getFullYear()) {
      maxMonth = maxDateObj.getMonth() + 1;
    }
  }

  let month = [];
  for (let i = minMonth; i <= maxMonth; i++) {
    const monthStr = i < 10 ? `0${i}` : i.toString();
    month.push({ text: monthStr + "月", value: monthStr });
  }

  return month;
}

// 生成日期选项，考虑日期范围限制
function generateDayOptions(year, month) {
  const daysInMonth = getCountDays(year, month);
  let minDay = 1;
  let maxDay = daysInMonth;

  if (props.minDate) {
    const minDateObj = new Date(props.minDate.replace(/-/g, "/"));
    if (year === minDateObj.getFullYear() && month === minDateObj.getMonth() + 1) {
      minDay = minDateObj.getDate();
    }
  }

  if (props.maxDate) {
    const maxDateObj = new Date(props.maxDate.replace(/-/g, "/"));
    if (year === maxDateObj.getFullYear() && month === maxDateObj.getMonth() + 1) {
      maxDay = maxDateObj.getDate();
    }
  }

  let day = [];
  for (let i = minDay; i <= maxDay; i++) {
    const dayStr = i < 10 ? `0${i}` : i.toString();
    day.push({ text: dayStr + "日", value: dayStr });
  }

  return day;
}

// 更新月份和日期列
function updateMonthAndDay(year, month) {
  // 更新月份列
  const monthOptions = generateMonthOptions(year);
  if (data.columns.length >= 2) {
    data.columns.splice(1, 1, monthOptions);
  }

  // 更新日期列
  const dayOptions = generateDayOptions(year, month);
  if (data.columns.length >= 3) {
    data.columns.splice(2, 1, dayOptions);
  } else {
    data.columns.push(dayOptions);
  }
}

/**
 * 显示弹框
 */
function showPicker() {
  data.isPicker = true;
  let momentDate;
  if (props.modelValue) {
    momentDate = moment(props.modelValue);
  } else {
    momentDate = moment();
  }

  const Y = momentDate.format("YYYY");
  const M = momentDate.format("MM"); // Already 1-based and zero-padded
  const D = momentDate.format("DD"); // Zero-padded
  const h = momentDate.format("HH"); // Zero-padded, 24-hour format
  const m = momentDate.format("mm"); // Zero-padded
  const s = momentDate.format("ss"); // Zero-padded

  // Clear and push new values
  data.selectedValues = [Y, M, D, h, m, s];
}

// 关闭弹框
function confirmOn() {
  data.isPicker = false;
}

//时间选择器关闭 值不改变并关闭弹框
function cancelOn() {
  confirmOn();
}

// 时间选择器确定 值改变
function onConfirm({ selectedValues }) {
  let endval =
    selectedValues[0] +
    "-" +
    selectedValues[1] +
    "-" +
    selectedValues[2] +
    " " +
    selectedValues[3] +
    ":" +
    selectedValues[4] +
    ":" +
    selectedValues[5];

  // 验证选择的日期是否在有效范围内
  if (!isDateInRange(endval)) {
    // 如果日期不在范围内，不更新值，只关闭弹框
    confirmOn();
    return;
  }

  confirmOn();
  displayValue.value = endval
  emit("update:modelValue", endval);
}

// 验证日期是否在有效范围内
function isDateInRange(dateStr) {
  const selectedDate = new Date(dateStr.replace(/-/g, "/"));

  if (props.minDate) {
    const minDate = new Date(props.minDate.replace(/-/g, "/"));
    if (selectedDate < minDate) {
      return false;
    }
  }

  if (props.maxDate) {
    const maxDate = new Date(props.maxDate.replace(/-/g, "/"));
    if (selectedDate > maxDate) {
      return false;
    }
  }

  return true;
}
</script>
