<!-- 专家抽取申请详情页面 -->
<template>
  <div class="expert-detail-page">
    <!-- 头部标题区域 -->
    <HeaderSection title="成都市农业农村局项目专家抽取表"></HeaderSection>
    <!-- 详情内容 -->
    <detailInfo :detailData="detailData"></detailInfo>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";

import HeaderSection from "@/components/HeaderSection.vue";
import detailInfo from "../components/detailInfo.vue";

const route = useRoute();
const router = useRouter();

// 响应式数据
const detailData = ref(null);

// 获取详情数据
const loadDetail = async () => {
  const id = route.params.id;
  const response = await expertApi.getApplyDetail(id);
  detailData.value = response.data;
};

loadDetail();
</script>

<style lang="scss" scoped>
.expert-detail-page {
}
</style>
<route lang="json5">
{
  name: "expertApplyDetail",
  meta: {
    title: "查看专家抽取申请",
  },
}
</route>
