<!-- 专家申请成功页面 -->
<template>
  <div class="success-page">
    <div class="success-content">
      <!-- 成功图标 -->
      <div class="success-icon">
        <van-icon name="checked" size="60" color="#07c160" />
      </div>

      <!-- 成功标题 -->
      <div class="success-title">{{ title || "申请成功" }}</div>

      <!-- 成功描述 -->
      <div class="success-desc">{{ desc || "请等待审核" }}</div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          type="success"
          size="large"
          block
          @click="goToRecords"
          class="primary-btn"
        >
          项目申请记录查询
        </van-button>

        <van-button
          type="default"
          size="large"
          block
          @click="goToHome"
          class="secondary-btn"
        >
          返回首页
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import { onMounted } from "vue";

const route = useRoute();
const router = useRouter();

const { title, desc } = route.query;

// 方法
const goToRecords = () => {
  // 跳转到项目申请记录查询页面
  router.replace("/user/project-records");
};

const goToHome = () => {
  // 返回首页
  router.back();
};
</script>

<style lang="scss" scoped>
.success-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-content {
  width: 100%;
  max-width: 320px;
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;

  .van-icon {
    background: #f0f9ff;
    border-radius: 50%;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.2);
  }
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
  line-height: 1.2;
}

.success-desc {
  font-size: 14px;
  color: #969799;
  margin-bottom: 48px;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.primary-btn {
  background: #07c160;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  height: 48px;

  &:active {
    background: #06ad56;
  }
}

.secondary-btn {
  background: #ffffff;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  color: #646566;
  font-size: 16px;
  height: 48px;

  &:active {
    background: #f7f8fa;
  }
}

// 动画效果
.success-icon {
  animation: bounceIn 0.6s ease-out;
}

.success-title {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.success-desc {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.action-buttons {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<route lang="json5">
{
  name: "Result",
  meta: {
    title: "结果页面",
  },
}
</route>
