<!-- 测试日期时间选择器组件 -->
<template>
  <div class="test-datetime-page">
    <van-nav-bar title="日期时间选择器测试" />
    
    <div class="content">
      <van-cell-group>
        <van-cell title="基础用法">
          <DateTimePicker
            v-model="basicValue"
            :field-props="{ label: '基础选择' }"
          />
        </van-cell>
        
        <van-cell title="限制最小日期">
          <DateTimePicker
            v-model="minDateValue"
            :min-date="minDate"
            :field-props="{ label: '最小日期限制' }"
          />
        </van-cell>
        
        <van-cell title="限制最大日期">
          <DateTimePicker
            v-model="maxDateValue"
            :max-date="maxDate"
            :field-props="{ label: '最大日期限制' }"
          />
        </van-cell>
        
        <van-cell title="限制日期范围">
          <DateTimePicker
            v-model="rangeValue"
            :min-date="minDate"
            :max-date="maxDate"
            :field-props="{ label: '日期范围限制' }"
          />
        </van-cell>
      </van-cell-group>
      
      <div class="result-section">
        <van-cell-group title="选择结果">
          <van-cell title="基础选择" :value="basicValue || '未选择'" />
          <van-cell title="最小日期限制" :value="minDateValue || '未选择'" />
          <van-cell title="最大日期限制" :value="maxDateValue || '未选择'" />
          <van-cell title="日期范围限制" :value="rangeValue || '未选择'" />
        </van-cell-group>
        
        <van-cell-group title="配置信息">
          <van-cell title="最小日期" :value="minDate" />
          <van-cell title="最大日期" :value="maxDate" />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

// 响应式数据
const basicValue = ref('')
const minDateValue = ref('')
const maxDateValue = ref('')
const rangeValue = ref('')

// 设置日期范围：当前日期前后30天
const minDate = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
const maxDate = moment().add(30, 'days').format('YYYY-MM-DD HH:mm:ss')
</script>

<style lang="scss" scoped>
.test-datetime-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}

.result-section {
  margin-top: 20px;
}
</style>

<route lang="json5">
{
  name: 'TestDateTime',
  meta: {
    title: '日期时间选择器测试'
  }
}
</route>
