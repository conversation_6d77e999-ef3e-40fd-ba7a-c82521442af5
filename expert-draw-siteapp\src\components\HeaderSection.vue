<template>
  <!-- 头部标题区域 -->
  <div class="header-section">
    <div class="header-content">
      <div class="title-line">{{ title }}</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: "",
});
</script>

<style lang="scss" scoped>
.header-section {
  background: linear-gradient(135deg, #07c160 0%, #05a854 100%);
  padding: 20px 16px;
  color: white;

  .header-content {
    text-align: center;

    .title-line {
      font-size: 18px;
      font-weight: 600;
      line-height: 1.4;
    }
  }
}
</style>
