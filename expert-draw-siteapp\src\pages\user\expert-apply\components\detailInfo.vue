<template>
  <div class="detail-info">
    <div v-if="detailData" class="detail-container">
      <!-- 需求部门显示 -->
      <div class="department-section">
        <van-cell-group>
          <van-cell
            title="需求部门"
            :value="detailData.department"
            label="机关处室/直属单位"
          />
        </van-cell-group>
      </div>

      <!-- 基本信息 -->
      <div class="info-section">
        <van-cell-group>
          <!-- 项目类型 -->
          <van-cell
            title="项目类型"
            :value="getProjectTypeText(detailData.type)"
          />

          <!-- 专项资金名称 -->
          <van-cell
            title="专项资金（项目）名称"
            :value="detailData.name || '-'"
          />

          <!-- 专项资金金额 -->
          <van-cell
            title="专项资金金额"
            :value="detailData.funds ? `${detailData.funds}万元` : '-'"
          />

          <!-- 评审开始时间 -->
          <van-cell
            title="评审开始时间"
            :value="formatDateTime(detailData.reviewDate)"
          />

          <!-- 预计天数 -->
          <van-cell
            title="预计天数"
            :value="detailData.dateNum ? `${detailData.dateNum}天` : '-'"
          />

          <!-- 评审地点 -->
          <van-cell title="评审地点" :value="detailData.reviewPlace || '-'" />
        </van-cell-group>
        <!-- 查看全部按钮 -->
        <!-- <div class="view-all-section">
          <van-button
            type="primary"
            plain
            size="small"
            @click="toggleExpertDetail"
          >
            {{ showExpertDetail ? "收起" : "查看全部" }}
          </van-button>
        </div> -->
      </div>

      <!-- 附件信息 -->
      <div class="attachment-section">
        <div class="section-title">附件信息</div>

        <!-- 项目评审工作方案备案表 -->
        <div class="attachment-item">
          <div class="attachment-label">项目评审工作方案备案表</div>
          <FileUploader v-model="detailData.attachments" disabled>
          </FileUploader>
        </div>

        <!-- 项目初审意见反馈表 -->
        <div class="attachment-item">
          <div class="attachment-label">项目初审意见反馈表</div>
          <FileUploader v-model="detailData.attachmentsFirst" disabled>
          </FileUploader>
        </div>

        <!-- 其他附件 -->
        <div class="attachment-item">
          <div class="attachment-label">其他</div>
          <FileUploader v-model="detailData.attachmentsOther" disabled>
          </FileUploader>
        </div>
      </div>

      <!-- 评审专家人员建议 -->
      <div class="expert-section">
        <div class="section-title">评审专家人员建议</div>

        <!-- 抽取专家类型 -->
        <van-cell-group>
          <van-cell
            title="抽取专家"
            :value="getExpertSourceText(detailData.proposalExpertSources)"
          />
        </van-cell-group>

        <!-- 专家类型和人数 -->
        <div class="expert-types">
          <div class="section-header">专家行业类别及人数</div>

          <div
            v-if="detailData.planDtos && detailData.planDtos.length > 0"
            class="expert-list"
          >
            <div
              class="expert-type-item"
              v-for="(item, index) in detailData.planDtos"
              :key="index"
            >
              <div class="type-row">
                <span class="type-label">{{
                  item.expertType?.name || "-"
                }}</span>
                <span class="type-count">{{ item.expertNum }}人</span>
              </div>
            </div>
          </div>
          <div v-else class="no-data">暂无专家类型配置</div>
        </div>

        <!-- 统计信息 -->
        <div class="expert-stats">
          <van-cell-group>
            <van-cell
              title="抽取专家人数"
              :value="`${detailData.planExpertPlNum || 0}人`"
            />
            <van-cell
              title="经办处室或直属单位人数"
              :value="`${detailData.departmentPlNum || 0}人`"
            />
            <van-cell
              title="邀请专家人数"
              :value="`${detailData.inviteExpertPlNum || 0}人`"
            />
            <van-cell
              title="审批领导"
              :value="detailData.approvalLeader || '-'"
            />
          </van-cell-group>
        </div>

        <!-- 补充说明 -->
        <div class="description-section">
          <van-cell-group>
            <van-cell
              title="补充说明"
              :value="detailData.description || '无'"
              class="description-cell"
            />
          </van-cell-group>
        </div>

        <!-- 申请人信息 -->
        <div class="applicant-section">
          <van-cell-group>
            <van-cell
              title="需求申请人姓名"
              :value="detailData.applyUserName || '-'"
            />
            <van-cell
              title="需求申请人联系方式"
              :value="detailData.applyUserPhone || '-'"
            />
          </van-cell-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import moment from "moment";

const props = defineProps({
  detailData: {
    type: Object,
  },
});

const showExpertDetail = ref(false);

// 切换专家详情显示
const toggleExpertDetail = () => {
  showExpertDetail.value = !showExpertDetail.value;
};

// 格式化时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "-";
  return moment(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

// 获取项目类型文本
const getProjectTypeText = (type) => {
  return window.$enums.getEnumText("ProjectType", type);
};

// 获取专家来源文本
const getExpertSourceText = (source) => {
  return window.$enums.getEnumText("ProposalExpertSourcesType", source);
};

// 预览文件
const previewFile = (file) => {
  showToast("文件预览功能开发中");
  // TODO: 实现文件预览功能
};
</script>

<style lang="scss" scoped>
.detail-info {
  .detail-container {
    padding-bottom: 20px;
  }

  .department-section,
  .info-section {
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    padding: 16px;
    background: white;
    margin-bottom: 0;
  }

  .attachment-section {
    margin-bottom: 12px;
    background: white;

    .attachment-item {
      padding: 16px;
      border-bottom: 1px solid #f7f8fa;

      &:last-child {
        border-bottom: none;
      }

      .attachment-label {
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 12px;
      }

      .attachment-list {
        .file-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          background: #f7f8fa;
          border-radius: 6px;
          margin-bottom: 8px;
          cursor: pointer;

          &:last-child {
            margin-bottom: 0;
          }

          .van-icon {
            color: #1989fa;
            margin-right: 8px;
          }

          .file-name {
            font-size: 14px;
            color: #323233;
          }

          &:active {
            background: #ebedf0;
          }
        }

        .no-files {
          color: #969799;
          font-size: 14px;
          text-align: center;
          padding: 20px;
        }
      }
    }
  }

  .expert-section {
    margin-bottom: 12px;

    .section-header {
      font-size: 14px;
      font-weight: 500;
      color: #323233;
      padding: 16px;
      background: white;
      border-bottom: 1px solid #f7f8fa;
    }

    .expert-list {
      background: white;

      .expert-type-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f7f8fa;

        &:last-child {
          border-bottom: none;
        }

        .type-row {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .type-label {
            font-size: 14px;
            color: #323233;
          }

          .type-count {
            font-size: 14px;
            color: #07c160;
            font-weight: 500;
          }
        }
      }
    }

    .no-data {
      padding: 20px;
      text-align: center;
      color: #969799;
      font-size: 14px;
      background: white;
    }
  }

  .expert-stats,
  .description-section,
  .applicant-section {
    margin-bottom: 12px;
  }

  .description-cell {
    :deep(.van-cell__value) {
      text-align: left;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .view-all-section {
    padding: 16px;
    text-align: center;
    border-top: 1px solid #f7f8fa;
  }
}
</style>
