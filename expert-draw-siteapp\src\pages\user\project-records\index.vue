<!-- 项目申请记录查询页面 -->
<template>
  <div class="records-page">
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      :search-placeholder="searchPlaceholder"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <template #item="{ item: record, index }">
        <!-- 项目标题 -->
        <div class="record-title">
          <div class="item-title">{{ record.name || "项目名称" }}</div>
          <span class="status-badge" :style="getStatuStyle(record.flowStatus)">
            {{ getStatusText(record.flowStatus) }}
          </span>
        </div>

        <!-- 项目信息 -->
        <div class="record-info">
          <div class="info-row">
            <span class="label">项目类型</span>
            <span class="value">{{ getProjectType(record.type) }}</span>
          </div>
          <div class="info-row">
            <span class="label">申请人</span>
            <span class="value">{{
              record.applyUserName || record.createUser?.nickname || "-"
            }}</span>
          </div>
          <div class="info-row">
            <span class="label">经办人处室</span>
            <span class="value">{{
              record.department || record.flowUser?.groups?.[0]?.name || "-"
            }}</span>
          </div>
          <div class="info-row">
            <span class="label">经办时间</span>
            <span class="value">{{
              formatDateTime(record.flowTime || record.createTime)
            }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="record-actions">
          <template v-if="hasRole('FLOW_START') && record.type == 'REVIEW'">
            <van-button
              size="small"
              plain
              v-if="record.flowStatus == 'UN_SUBMIT'"
              @click.stop="clickHande('edit', record)"
            >
              编辑
            </van-button>
            <van-button
              size="small"
              plain
              v-else-if="record.flowStatus == 'APPROVAL'"
              @click.stop="clickHande('progress', record)"
            >
              查看审批进度
            </van-button>
            <template v-else-if="record.flowStatus == 'FINISH'">
              <van-button
                size="small"
                plain
                @click.stop="clickHande('extract', record)"
              >
                抽取专家
              </van-button>
              <!-- <van-button size="small" plain @click="clickHande('extract',record)">
                专家抽取情况
              </van-button> -->
            </template>
          </template>
          <template v-if="hasRole('FLOW_BM') && record.type == 'EVADE'">
            <van-button
              size="small"
              plain
              v-if="['REJECT','APPROVAL'].includes(record.flowStatus)"
              @click.stop="clickHande('EVADEProgress', record)"
            >
              查看审批进度
            </van-button>
          </template>
          <template v-if="record.type == 'PURCHASE'">
             <van-button
              size="small"
              plain
              v-if="['FINISH'].includes(record.flowStatus) && hasRole('FLOW_START')"
              @click.stop="clickHande('PURCHASEExtract', record)"
            >
              抽取代理机构
            </van-button>
            <van-button
              size="small"
              plain
              v-if="['REJECT','APPROVAL'].includes(record.flowStatus)"
              @click.stop="clickHande('PURCHASEProgress', record)"
            >
              查看审批进度
            </van-button>
          </template>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import moment from "moment";
import { expertApi } from "@/api/expert";
import { useUserStore } from "@/stores";

const router = useRouter();
const recordsListRef = ref();
const { hasRole } = useUserStore();

//tabs徽标
const extras = ref({});

// 搜索placeholder
const searchPlaceholder = ref("模糊检索项目名称/编号");

// Tabs 配置
const tabsConfig = computed(() => [
  {
    key: "APPLY",
    title: "已发起",
    loadFunction: loadApplyRecords,
    extraParams: {
      districtDep: "false",
      todoStatus: "APPLY",
    },
    keyword: "nameCodeSearchText",
    filterConfig: getFilterConfig(),
  },
  {
    key: "TODO",
    title: "待审批",
    loadFunction: loadTodoRecords,
    badge: extras.value.toDoNum,
    extraParams: {
      districtDep: "false",
      todoStatus: "TODO",
    },
    keyword: "nameCodeSearchText",
    filterConfig: getFilterConfig(),
  },
  {
    key: "DONE",
    title: "已审批",
    loadFunction: loadDoneRecords,
    extraParams: {
      districtDep: "false",
      todoStatus: "DONE",
    },
    keyword: "nameCodeSearchText",
    filterConfig: getFilterConfig(),
  },
]);

// 筛选配置
const getFilterConfig = () => [
  {
    type: "input",
    label: "经办人处室",
    prop: "flowUserPostSearchText",
    props: {
      placeholder: "请输入",
      clearable: true,
    },
  },
  {
    type: "datetime",
    label: "经办开始时间",
    prop: "flowTimeStart",
    props: {},
  },
  {
    type: "datetime",
    label: "经办结束时间",
    prop: "flowTimeEnd",
    props: {},
  },
  {
    type: "select",
    label: "项目类型",
    prop: "applyType",
    options: window.$enums.getEnums("ApplyType"),
  },
  {
    type: "select",
    label: "项目状态",
    prop: "flowStatus",
    options: window.$enums.getEnums("FlowStatus"),
  },
];

// 数据加载函数
async function loadApplyRecords(params) {
  const response = await expertApi.getApplyList(params);
  extras.value = response.extras;
  return {
    data: response.data || [],
    total: response.total || 0,
  };
}

async function loadTodoRecords(params) {
  const response = await expertApi.getApplyList(params);
  extras.value = response.extras;
  return {
    data: response.data || [],
    total: response.total || 0,
  };
}

async function loadDoneRecords(params) {
  const response = await expertApi.getApplyList(params);
  extras.value = response.extras;
  return {
    data: response.data || [],
    total: response.total || 0,
  };
}

// 事件处理方法
const handleItemClick = (record, index) => {
  viewDetail(record);
};

const handleTabChange = (tabKey, tab) => {
  console.log("Tab 切换:", tabKey, tab);
};

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";
  return moment(dateTime).format("YYYY-MM-DD HH:mm");
};

const getProjectType = (type) => {
  return window.$enums.getEnumText("ApplyType", type);
};

const getStatusText = (status) => {
  return window.$enums.getEnumText("FlowStatus", status);
};

const getStatuStyle = (status) => {
  let color = window.$enums.getEnumColor("FlowStatus", status);
  return {
    color,
    backgroundColor: color + "10",
  };
};

const viewDetail = (record) => {
  if(record.type == 'PURCHASE'){
    router.push(`/user/agency-list/detail/${record.entityId}`);
  }
  else if (record.type == 'EVADE') {
    router.push(`/user/expert-apply/evade/detail/${record.entityId}`);
  } else {
    router.push(`/user/expert-apply/detail/${record.entityId}`);
  }
};

const clickHande = (type, record) => {
  switch (type) {
    case "edit":
      router.push(`/user/expert-apply?id=${record.entityId}`);
      break;
    case "progress":
      router.push(`/user/expert-apply/progress/${record.entityId}`);
      break;
    case "EVADEProgress":
      router.push(`/user/expert-apply/evade/progress/${record.entityId}`);
      break;
    case "PURCHASEProgress":
      router.push(`/user/agency-list/progress/${record.entityId}`);
      break;
    case "extract":
      router.push(`/user/expert-apply/extract/${record.entityId}`);
      break;
    case "PURCHASEExtract":
      router.push(`/user/agency-list/extract/${record.entityId}`);
      break;
  }
};
</script>

<style lang="scss" scoped>
.records-page {
  height: calc(100vh - 46px);
  background: #f5f5f5;
}

.record-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.record-title {
  font-size: 15px;
  font-weight: 500;
  color: #323233;
  line-height: 1.4;
  margin-bottom: 12px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .item-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-left: 8px;
  flex-shrink: 0;

  &.status-unsubmit {
    background: #f0f0f0;
    color: #666;
  }

  &.status-approval {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.status-reject {
    background: #fff2f0;
    color: #ff4d4f;
  }

  &.status-revoke {
    background: #fff7e6;
    color: #fa8c16;
  }

  &.status-finish {
    background: #f6ffed;
    color: #52c41a;
  }
}

.record-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  color: #646566;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #323233;
  flex: 1;
}

.record-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;

  .van-button {
    border-radius: 16px;
    font-size: 12px;
    padding: 0 12px;
    height: 28px;
  }
}
</style>

<route lang="json5">
{
  name: "projectList",
  meta: {
    title: "申请记录",
  },
}
</route>
