<!-- 抽取代理机构列表页面 -->
<template>
  <div class="agency-list-page">
    <FuniList ref="agencyListRef" :tabs="tabsConfig">
      <template #item="{ item, index }">
        <div class="project-item" @click="itemClick(item)">
          <!-- 项目标题和状态 -->
          <div class="project-header">
            <div class="project-title">
              {{ item.name }}
            </div>
            <div class="project-status" :class="getStatusClass(item)">
              {{ getStatusText(item) }}
            </div>
          </div>

          <!-- 项目信息 -->
          <div class="project-info">
            <div class="info-row">
              <span class="label">项目分类</span>
              <span class="value">{{ getProjectType(item.type) }}</span>
            </div>
            <div class="info-row">
              <span class="label">承办处室</span>
              <span class="value">{{ item.department?.name }}</span>
            </div>
            <div class="info-row">
              <span class="label">采购品目</span>
              <span class="value">{{ item.items }}</span>
            </div>
            <div class="info-row">
              <span class="label">采购内容(用途)</span>
              <span class="value">{{ item.purpose }}</span>
            </div>
            <div class="info-row">
              <span class="label">预算分类</span>
              <span class="value">{{ getProjectPlanType(item.planType) }}</span>
            </div>
            <div class="info-row">
              <span class="label">无预算项目</span>
              <span class="value">{{ item.priceUnit ? "是" : "否" }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <van-button
              v-if="!item.drew"
              class="action-btn secondary"
              @click="handleCancel(item)"
            >
              作废
            </van-button>
            <van-button
              type="primary"
              class="action-btn primary"
              @click="handleApply(item)"
            >
              采购代理申请
            </van-button>
          </div>
        </div>
      </template>
    </FuniList>
    <!-- 底部添加按钮 -->
    <div class="bottom-add-button">
      <van-button type="primary" class="add-btn" @click="handleAddProject">
        <van-icon name="plus" />
        无预算项目前期工作抽取代理机构
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { showToast, showConfirmDialog } from "vant";
import { expertApi } from "@/api/expert";

const router = useRouter();

// Tabs 配置
const tabsConfig = [
  {
    key: "NORMAL",
    title: "正常",
    loadFunction: async (params) => {
      const response = await expertApi.getProjectList(params);
      // 处理返回数据
      return {
        data: response.data,
        total: response.total,
      };
    },
    extraParams: {
      projectStatus: "NORMAL",
    },
    searchPlaceholder: "项目名称",
    keyword: "name",
    filterConfig: [
      {
        type: "input",
        label: "承办处室",
        prop: "departmentName",
      },
      {
        type: "input",
        label: "项目情况",
        prop: "searchText",
      },
      {
        type: "select",
        label: "项目分类",
        prop: "type",
        options: window.$enums.getEnums("ProjectType"),
      },
    ],
  },
  {
    key: "CANCEL",
    title: "作废",
    loadFunction: async (params) => {
      const response = await expertApi.getProjectList(params);
      // 处理返回数据
      return {
        data: response.data,
        total: response.total,
      };
    },
    extraParams: {
      projectStatus: "CANCEL",
    },
    searchPlaceholder: "项目名称",
    keyword: "name",
    filterConfig: [
      {
        type: "input",
        label: "承办处室",
        prop: "departmentName",
      },
      {
        type: "input",
        label: "项目情况",
        prop: "searchText",
      },
      {
        type: "select",
        label: "项目分类",
        prop: "type",
        options: window.$enums.getEnums("ProjectType"),
      },
    ],
  },
];

function getStatusClass(status) {
  return "normal";
}

function getStatusText(item) {
  if (!item.mainApply) {
    return "未申请";
  } else {
    window.$enums.getEnumText("FlowStatus", item.mainApply?.flowStatus);
  }
}

function getProjectType(val) {
  return window.$enums.getEnumText("ProjectType", val);
}
function getProjectPlanType(val) {
  return window.$enums.getEnumText("ProjectPlanType", val);
}

function itemClick(item) {
  //暂无详情接口
  // router.push("/user/agency-list/detail/"+item.id)
}
function handleAddProject() {
  router.push("/user/agency-list/add");
}
async function handleCancel(item) {
  await showConfirmDialog({
    title: "提示",
    message: "你确定要作废这条记录吗？",
  });
  expertApi.projectCancel(item.id);
}

function handleApply(item) {
  router.push("/user/agency-list/apply/" + item.id);
}
</script>

<style lang="scss" scoped>
.agency-list-page {
  height: calc(100vh - 46px);
}

.project-list {
  padding: 12px 16px;
}

.project-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .project-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    line-height: 1.4;
    margin-right: 12px;
  }

  .project-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;

    &.normal {
      background: #f0f9ff;
      color: #1890ff;
    }

    &.applied {
      background: #f6ffed;
      color: #52c41a;
    }

    &.cancelled {
      background: #fff2f0;
      color: #ff4d4f;
    }
  }
}

.project-info {
  margin-bottom: 16px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f7f8fa;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 14px;
      color: #646566;
      min-width: 80px;
    }

    .value {
      font-size: 14px;
      color: #323233;
      text-align: right;
      flex: 1;
      margin-left: 12px;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;

  .action-btn {
    flex: 1;
    height: 36px;
    border-radius: 6px;
    font-size: 14px;

    &.secondary {
      background: #f7f8fa;
      color: #646566;
      border: 1px solid #ebedf0;
    }

    &.primary {
      background: #07c160;
      border: none;
    }

    &.detail {
      background: #ff8c00;
      color: white;
      border: none;
    }
  }
}

.bottom-add-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 100;

  .add-btn {
    width: 100%;
    height: 44px;
    background: #07c160;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .van-icon {
      font-size: 16px;
    }
  }
}
</style>
