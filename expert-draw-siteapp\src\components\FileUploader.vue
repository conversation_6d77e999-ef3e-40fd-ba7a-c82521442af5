<!-- 公共文件上传组件 -->
<template>
  <van-field
    v-bind="fieldProps"
    :required="required"
    :disabled="disabled"
    :rules="rules"
    :class="customClass"
  >
    <template #input>
      <van-uploader
        v-if="(!disabled || fileList.length)"
        v-model="fileList"
        :multiple="multiple"
        :max-count="maxCount"
        :max-size="maxSizeBytes"
        :accept="accept"
        :disabled="disabled"
        :deletable="!disabled"
        :show-upload="!disabled && fileList.length < maxCount"
        :after-read="afterRead"
        :before-delete="beforeDelete"
        @delete="onDelete"
        @oversize="onOversize"
        class="file-uploader"
      >
        <!-- <template #preview-cover="{ file }">
          <div class="preview-cover van-ellipsis">
            {{ file.name || '文件' }}
          </div>
        </template> -->

        <template #upload>
          <div class="upload-area">
            <van-icon name="plus" size="24" />
            <div class="upload-text">{{ uploadText }}</div>
          </div>
        </template>
      </van-uploader>
      <van-empty v-else description="暂无附件" style="width:100%;height:50px" image-size="50" />
    </template>
  </van-field>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { showToast, showLoadingToast, closeToast } from "vant";

defineOptions({
  name: "FileUploader",
});

const props = defineProps({
  // 绑定值
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false,
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 9,
  },
  // 文件大小限制 M
  maxSize: {
    type: Number,
    default: 10,
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: "image/*",
  },
  // 上传接口地址
  uploadUrl: {
    type: String,
    default: "/api/v1/upload-file",
  },
  // 文件读取接口前缀
  readUrlPrefix: {
    type: String,
    default: "/csccs/file/read?id=",
  },
  // 上传按钮文字
  uploadText: {
    type: String,
    default: "上传文件",
  },
  // 字段属性
  fieldProps: {
    type: Object,
    default: () => ({}),
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: "",
  },
  // 验证规则
  rules: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(["update:modelValue", "change"]);

// 响应式数据
const fileList = ref([]);

// 计算属性
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024);

// 方法
const afterRead = (file) => {
  const files = Array.isArray(file) ? file : [file];

  for (const fileItem of files) {
    uploadFile(fileItem);
  }
  return false;
};

const uploadFile = async (fileItem) => {
  try {
    // 设置上传状态
    fileItem.status = "uploading";
    fileItem.message = "上传中...";

    // 创建FormData
    const formData = new FormData();
    formData.append("uploadFile", fileItem.file);

    // 上传文件
    const response = await window.$http.upload(props.uploadUrl, formData);
    if (response) {
      // 上传成功
      fileItem.status = "done";
      fileItem.id = response[0];
      fileItem.name = fileItem.file.name;
      fileItem.message = "";

      // 判断是否为图片
      const isImage = fileItem.file.type?.includes("image");
      fileItem.isImage = isImage;
      updateModelValue();
    } else {
      throw new Error("上传失败");
    }
  } catch (error) {
    fileItem.status = "failed";
    fileItem.message = "上传失败";
    showToast("文件上传失败");
  }
};

const beforeDelete = (file) => {
  return new Promise((resolve) => {
    resolve(true);
  });
};

const onDelete = (file, detail) => {
  const index = detail.index;
  fileList.value.splice(index, 1);
  updateModelValue();
};

const onOversize = () => {
  showToast(`文件大小不能超过 ${props.maxSize}MB`);
};

// 更新绑定值
const updateModelValue = () => {
  const validFiles = fileList.value
    .filter((file) => file.status === "done" && file.id)
    .map((file) => ({
      id: file.id,
      name: file.name,
      url: file.url,
      isImage: file.isImage,
    }));
  emits("update:modelValue", validFiles);
  emits("change", validFiles);
};

// 根据文件ID获取文件信息
const getFileById = async (fileId) => {
  try {
    const response = await window.$http.post(
      `${props.readUrlPrefix}${fileId}`,
      {},
      {
        responseType: "blob",
        headers: {
          "Content-Type": "application/octet-stream;charset=utf-8",
        },
      }
    );
    return response;
  } catch (error) {
    console.error("获取文件失败:", error);
    return null;
  }
};

/**
 * 判断是否图片
 * @param filename
 */
function isImage(filename) {
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".webp",
    ".svg",
  ];
  const ext = filename.slice(filename.lastIndexOf(".")).toLowerCase();
  return imageExtensions.includes(ext);
}

/**
 * 获取url参数
 * @param url
 */
function getUrlParams(url) {
  const params = {};
  const queryString = url.split("?")[1];

  if (queryString) {
    queryString.split("&").forEach((pair) => {
      const [key, value] = pair.split("=");
      params[key] = decodeURIComponent(value || "");
    });
  }

  return params;
}
// 初始化文件列表
const initFileList = async () => {
  if (!props.modelValue || props.modelValue.length === 0) {
    fileList.value = [];
    return;
  }

  const newFileList = [];

  for (const item of props.modelValue) {
    let fileItem = {
      id: item.id,
      name: item.name || "文件",
      url: item.url,
      isImage: item.isImage || false,
      status: "done",
    };
    if (typeof item == "string") {
      let obj = getUrlParams(item);
      fileItem = {
        id: item,
        url: item,
        name: obj.originalFilename || "文件",
        isImage: isImage(obj.originalFilename),
        status: "done",
      };
    }

    // 如果是图片且没有url，尝试获取
    if (fileItem.isImage && !fileItem.url && fileItem.id) {
      try {
        const blob = await getFileById(fileItem.id);
        if (blob) {
          fileItem.url = URL.createObjectURL(blob);
        }
      } catch (error) {
        console.error("获取图片预览失败:", error);
      }
    }

    newFileList.push(fileItem);
  }

  fileList.value = newFileList;
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (
      JSON.stringify(newValue) !==
      JSON.stringify(fileList.value.filter((f) => f.status === "done"))
    ) {
      initFileList()
    }
  },
  { deep: true }
);

// 生命周期
onMounted(() => {
  initFileList();
});
</script>

<style lang="scss" scoped>
.file-uploader {
  :deep(.van-uploader__upload) {
    position: relative;
    margin: 0 8px 8px 0;
  }

  :deep(.van-uploader__preview) {
    position: relative;
    margin: 0 8px 8px 0;
  }
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: var(--van-background-2);
  border: 1px dashed var(--van-border-color);
  border-radius: 8px;
  color: var(--van-text-color-2);

  .upload-text {
    margin-top: 4px;
    font-size: 12px;
    text-align: center;
  }
}

.preview-cover {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  text-align: center;
  border-radius: 0 0 8px 8px;
}
</style>
