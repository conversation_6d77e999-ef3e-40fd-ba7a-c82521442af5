<!-- 申请进度查看页面 -->
<template>
  <div class="progress-page">
    <AuthStep ref="authStepRef" :flows="progressData?.flows"></AuthStep>

    <!-- 详情内容 -->
    <detailInfo :detailData="progressData" style="padding: 0 16px"></detailInfo>

    <!-- 底部操作按钮 -->
    <div v-if="progressData && hasRole('FLOW_BM') " class="bottom-actions">
      <van-button class="back-btn" @click="goBack"> 返回 </van-button>
      <van-button
        type="primary"
        class="submit-btn"
        :loading="submitting"
        :disabled="submitting"
        @click="submitProgress"
      >
        {{ submitting ? "提交中..." : "提交审批" }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";
import moment from "moment";
import { useUserStore } from "@/stores";
import AuthStep from "@/components/AuthStep.vue";
import detailInfo from "../components/detailInfo.vue";


const route = useRoute();
const router = useRouter();
const { hasRole } = useUserStore();
const authStepRef = ref()
// 响应式数据
const loading = ref(true);
const progressData = ref(null);
const approvalFormRef = ref(null);
const submitting = ref(false);

// 获取进度数据
const loadProgress = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    if (!id) {
      showToast("缺少申请ID");
      router.back();
      return;
    }

    const response = await expertApi.getApplyDetail(id);
    progressData.value = response.data;
  } catch (error) {
    console.error("加载进度失败:", error);
    showToast("加载进度失败");
  } finally {
    loading.value = false;
  }
};

// 返回
const goBack = () => {
  router.back();
};

// 提交审批（底部按钮）
const submitProgress = async () => {
  try {
    // 表单验证
    let res = await authStepRef.value.validate();
    // 调用审批接口
    await expertApi.submitApproval(res);
    router.replace("/pub/result?title=审批完成&desc=");
  } finally {
    submitting.value = false;
  }
};

// 页面初始化
onMounted(() => {
  loadProgress();
});
</script>

<style lang="scss" scoped>
.progress-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #969799;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  padding: 20px;

  .van-button {
    margin-top: 16px;
  }
}

.progress-container {
  padding: 16px;
}

.steps-section {
  background: white;
  padding: 20px 16px;
  margin-bottom: 12px;
  border-radius: 8px;
}

.custom-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;

  &:not(:last-child) {
    margin-right: 16px;
  }
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ebedf0;
  color: #969799;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  z-index: 2;
  position: relative;

  &.active {
    background: #07c160;
    color: white;
  }
}

.step-title {
  font-size: 12px;
  color: #646566;
  text-align: center;
  line-height: 1.2;
}

.step-line {
  position: absolute;
  top: 12px;
  left: calc(50% + 12px);
  right: calc(-50% + 12px);
  height: 1px;
  background: #ebedf0;
  z-index: 1;

  &.active {
    background: #07c160;
  }
}

.step-item:last-child .step-line {
  display: none;
}

.no-steps {
  text-align: center;
  padding: 20px;
  color: #969799;
  font-size: 14px;
}

.section {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;
}

.section-header {
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;

  .project-code {
    display: block;
    font-size: 14px;
    color: #646566;
    margin-bottom: 4px;
  }

  .project-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    line-height: 1.4;
  }
}

.approval-section {
  &.readonly {
    background: #fafafa;
  }

  .approval-opinion {
    margin-bottom: 16px;

    .opinion-label {
      font-size: 14px;
      color: #323233;
      margin-bottom: 12px;
      font-weight: 500;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .opinion-radios {
      display: flex;
      gap: 24px;

      .opinion-radio {
        font-size: 14px;

        :deep(.van-radio__label) {
          color: #323233;
          margin-left: 8px;
        }

        :deep(.van-radio__icon--checked) {
          .van-icon {
            background: #07c160;
            border-color: #07c160;
          }
        }
      }
    }
  }

  .approval-result {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .result-label {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      margin-right: 12px;
    }

    .result-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      &.pass {
        background: #f0f9ff;
        color: #07c160;
        border: 1px solid #07c160;
      }

      &.reject {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ff4d4f;
      }
    }
  }

  .approval-comment {
    .comment-field {
      :deep(.van-field__label) {
        font-size: 14px;
        color: #323233;
        font-weight: 500;

        &::after {
          content: "*";
          color: #ff4d4f;
          margin-left: 2px;
        }
      }

      :deep(.van-field__control) {
        font-size: 14px;
        line-height: 1.5;
      }

      :deep(.van-field__word-limit) {
        color: #969799;
        font-size: 12px;
      }
    }

    &.readonly {
      .comment-label {
        font-size: 14px;
        color: #323233;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .comment-content {
        padding: 12px;
        background: white;
        border-radius: 6px;
        font-size: 14px;
        color: #646566;
        line-height: 1.5;
        border: 1px solid #ebedf0;
      }
    }
  }
}

.view-all-section {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #f7f8fa;
}

.expert-detail-section {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;

  .section-header {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #f7f8fa;
  }

  .expert-list {
    background: white;

    .expert-type-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f7f8fa;

      &:last-child {
        border-bottom: none;
      }

      .type-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .type-label {
          font-size: 14px;
          color: #323233;
        }

        .type-count {
          font-size: 14px;
          color: #07c160;
          font-weight: 500;
        }
      }
    }
  }

  .no-data {
    padding: 20px;
    text-align: center;
    color: #969799;
    font-size: 14px;
    background: white;
  }

  .expert-stats,
  .description-section,
  .applicant-section {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .description-cell {
    :deep(.van-cell__value) {
      text-align: left;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  gap: 12px;
  z-index: 100;

  .back-btn {
    flex: 1;
    height: 44px;
    border: 1px solid #ebedf0;
    background: white;
    color: #646566;
  }

  .submit-btn {
    flex: 1;
    height: 44px;
    background: #07c160;
    border: none;
  }
}

// 覆盖vant样式
:deep(.van-cell) {
  padding: 12px 16px;

  .van-cell__title {
    font-size: 14px;
    color: #323233;
  }

  .van-cell__value {
    font-size: 14px;
    color: #646566;
  }
}
</style>
<route lang="json5">
{
  name: "ExpertProgress",
  meta: {
    title: "申请进度查询",
  },
}
</route>
