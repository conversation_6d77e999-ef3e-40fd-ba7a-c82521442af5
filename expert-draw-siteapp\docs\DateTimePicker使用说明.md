# DateTimePicker 日期时间选择器组件使用说明

## 概述

DateTimePicker 是一个基于 Vant UI 的日期时间选择器组件，支持年月日时分秒的选择，并新增了最小日期和最大日期的限制功能。

## 新增功能

### 最小日期和最大日期限制

组件现在支持通过 `minDate` 和 `maxDate` 属性来限制用户可选择的日期范围。

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Array | [] | 绑定值 |
| showPicker | Boolean | false | 是否显示选择器 |
| values | String | "" | 传入的值 |
| fieldProps | Object | {} | 字段属性 |
| **minDate** | **String** | **""** | **最小可选日期，格式：YYYY-MM-DD HH:mm:ss** |
| **maxDate** | **String** | **""** | **最大可选日期，格式：YYYY-MM-DD HH:mm:ss** |

## 使用示例

### 基础用法

```vue
<template>
  <DateTimePicker
    v-model="dateValue"
    :field-props="{ label: '选择日期' }"
  />
</template>

<script setup>
import { ref } from 'vue'

const dateValue = ref('')
</script>
```

### 限制最小日期

```vue
<template>
  <DateTimePicker
    v-model="dateValue"
    :min-date="minDate"
    :field-props="{ label: '选择日期' }"
  />
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

const dateValue = ref('')
const minDate = moment().format('YYYY-MM-DD HH:mm:ss') // 当前时间为最小日期
</script>
```

### 限制最大日期

```vue
<template>
  <DateTimePicker
    v-model="dateValue"
    :max-date="maxDate"
    :field-props="{ label: '选择日期' }"
  />
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

const dateValue = ref('')
const maxDate = moment().add(30, 'days').format('YYYY-MM-DD HH:mm:ss') // 30天后为最大日期
</script>
```

### 限制日期范围

```vue
<template>
  <DateTimePicker
    v-model="dateValue"
    :min-date="minDate"
    :max-date="maxDate"
    :field-props="{ label: '选择日期' }"
  />
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

const dateValue = ref('')
const minDate = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss') // 7天前
const maxDate = moment().add(7, 'days').format('YYYY-MM-DD HH:mm:ss') // 7天后
</script>
```

## 功能特性

1. **智能年份范围**：根据 minDate 和 maxDate 自动计算可选年份范围
2. **动态月份选项**：根据选择的年份和日期限制动态生成可选月份
3. **动态日期选项**：根据选择的年份、月份和日期限制动态生成可选日期
4. **日期验证**：确认选择时验证日期是否在有效范围内
5. **联动更新**：年份或月份变化时自动更新相关选项

## 注意事项

1. 日期格式必须为 `YYYY-MM-DD HH:mm:ss`
2. 如果设置的 minDate 或 maxDate 格式不正确，组件会忽略该限制
3. 当用户选择的日期超出范围时，组件会阻止更新值并关闭选择器
4. 建议使用 moment.js 来格式化日期字符串

## 测试页面

可以访问 `/test-datetime` 页面查看组件的各种使用场景和效果。
