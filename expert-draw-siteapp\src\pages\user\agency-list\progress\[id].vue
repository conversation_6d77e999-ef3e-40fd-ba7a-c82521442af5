<!-- 测试预算项目金额抽取代理机构申请表 -->
<template>
  <div class="agency-apply-page">
    <!-- 详情内容 -->
    <AuthStep ref="authStepRef" :flows="projectData?.flows"></AuthStep>
    <detailInfo :projectData="projectData"></detailInfo>
    <!-- 底部操作按钮 -->
    <div v-if="projectData" class="bottom-actions">
      <van-button class="back-btn" @click="goBack"> 返回 </van-button>
      <van-button
        type="primary"
        class="submit-btn"
        :loading="submitting"
        :disabled="submitting"
        @click="submitProgress"
      >
        {{ submitting ? "提交中..." : "提交审批" }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { detail } from "@/api/procurement";
import { expertApi } from "@/api/expert";
import { useUserStore } from "@/stores";
import AuthStep from "@/components/AuthStep.vue";
import detailInfo from "../components/detailInfo.vue";

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
//项目数据
const projectData = ref();
const authStepRef = ref()


// 获取项目详情数据
const loadProjectDetail = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await detail(id);
    projectData.value = response.data;
  } finally {
    loading.value = false;
  }
};
// 返回
const goBack = () => {
  router.back();
};

// 提交审批（底部按钮）
const submitProgress = async () => {
  let res =  await authStepRef.value.validate();
    await expertApi.submitApproval(res);
    router.replace("/pub/result?title=审批完成&desc=");
};

loadProjectDetail();
</script>

<style lang="scss" scoped>
.agency-apply-page {
    padding-bottom: 70px;
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 12px 16px;
    border-top: 1px solid #ebedf0;
    display: flex;
    gap: 12px;
    z-index: 100;

    .back-btn {
      flex: 1;
      height: 44px;
      border: 1px solid #ebedf0;
      background: white;
      color: #646566;
    }

    .submit-btn {
      flex: 1;
      height: 44px;
      background: #07c160;
      border: none;
    }
  }
}
</style>

<route lang="json5">
{
  name: "AgencyProgress",
  meta: {
    title: "抽取代理机构申请详情",
    requiresAuth: true,
    keepAlive: false,
  },
}
</route>
