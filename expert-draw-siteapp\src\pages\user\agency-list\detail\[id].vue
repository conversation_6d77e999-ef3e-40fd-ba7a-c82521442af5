<!-- 测试预算项目金额抽取代理机构申请表 -->
<template>
  <div class="agency-apply-page">
     <!-- 头部标题区域 -->
    <HeaderSection :title="'【' +(projectData?.project?.name  || '')+'】抽取代理机构申请表'"></HeaderSection>
    <!-- 详情内容 -->
    <detailInfo :projectData="projectData"></detailInfo>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { detail } from "@/api/procurement";
import { useUserStore } from "@/stores";
import HeaderSection from "@/components/HeaderSection.vue";
import detailInfo from "../components/detailInfo.vue";

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
//项目数据
const projectData = ref();


// 获取项目详情数据
const loadProjectDetail = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await detail(id);
    projectData.value = response.data;
  } finally {
    loading.value = false;
  }
};


loadProjectDetail();
</script>

<style lang="scss" scoped>
.agency-apply-page {

}
</style>

<route lang="json5">
{
  name: "AgencyDetail",
  meta: {
    title: "抽取代理机构申请详情",
    requiresAuth: true,
    keepAlive: false,
  },
}
</route>
